'use client'
import { useRef, useState, useCallback } from 'react'
import { useFrame } from '@react-three/fiber'
import * as THREE from 'three'

// Random object generator utility
export class RandomObjectGenerator {
  static geometries = [
    { type: 'box', args: () => [0.1, 0.1, 0.1] },
    { type: 'sphere', args: () => [0.05, 16, 16] },
    { type: 'cylinder', args: () => [0.05, 0.05, 0.1, 16] },
    { type: 'cone', args: () => [0.05, 0.1, 16] },
    { type: 'torus', args: () => [0.04, 0.02, 8, 16] },
    { type: 'octahedron', args: () => [0.06] },
    { type: 'dodecahedron', args: () => [0.05] },
    { type: 'icosahedron', args: () => [0.05] },
  ]

  static materials = [
    { type: 'basic', color: () => new THREE.Color().setHSL(Math.random(), 0.8, 0.6) },
    { type: 'standard', color: () => new THREE.Color().setHSL(Math.random(), 0.7, 0.5) },
    { type: 'phong', color: () => new THREE.Color().setHSL(Math.random(), 0.9, 0.7) },
    { type: 'lambert', color: () => new THREE.Color().setHSL(Math.random(), 0.6, 0.8) },
  ]

  static animations = [
    'rotate',
    'bounce',
    'float',
    'pulse',
    'wobble',
    'spin',
    'none'
  ]

  static generateRandomObject() {
    const geometry = this.geometries[Math.floor(Math.random() * this.geometries.length)]
    const material = this.materials[Math.floor(Math.random() * this.materials.length)]
    const animation = this.animations[Math.floor(Math.random() * this.animations.length)]
    
    return {
      id: Math.random().toString(36).substr(2, 9),
      geometry: {
        type: geometry.type,
        args: geometry.args()
      },
      material: {
        type: material.type,
        color: material.color(),
        transparent: Math.random() > 0.7,
        opacity: Math.random() * 0.5 + 0.5,
        metalness: Math.random(),
        roughness: Math.random()
      },
      animation,
      scale: Math.random() * 0.5 + 0.5,
      rotation: {
        x: Math.random() * Math.PI * 2,
        y: Math.random() * Math.PI * 2,
        z: Math.random() * Math.PI * 2
      }
    }
  }
}

// Individual AR object component
export function ARObject({ 
  objectData, 
  position, 
  onRemove,
  autoRemove = false,
  autoRemoveDelay = 10000 
}) {
  const meshRef = useRef()
  const [startTime] = useState(Date.now())
  const animationSpeed = useRef(Math.random() * 2 + 1)

  // Auto-remove after delay
  useState(() => {
    if (autoRemove) {
      const timer = setTimeout(() => {
        onRemove?.(objectData.id)
      }, autoRemoveDelay)
      
      return () => clearTimeout(timer)
    }
  }, [autoRemove, autoRemoveDelay, objectData.id, onRemove])

  // Animation frame
  useFrame((state) => {
    if (!meshRef.current) return

    const elapsed = state.clock.elapsedTime
    const mesh = meshRef.current

    switch (objectData.animation) {
      case 'rotate':
        mesh.rotation.y = elapsed * animationSpeed.current
        break
        
      case 'bounce':
        mesh.position.y = position[1] + Math.sin(elapsed * 3) * 0.05
        break
        
      case 'float':
        mesh.position.y = position[1] + Math.sin(elapsed * 2) * 0.02
        mesh.rotation.y = elapsed * 0.5
        break
        
      case 'pulse':
        const scale = objectData.scale * (1 + Math.sin(elapsed * 4) * 0.2)
        mesh.scale.setScalar(scale)
        break
        
      case 'wobble':
        mesh.rotation.x = Math.sin(elapsed * 3) * 0.2
        mesh.rotation.z = Math.cos(elapsed * 2) * 0.1
        break
        
      case 'spin':
        mesh.rotation.x = elapsed * animationSpeed.current
        mesh.rotation.y = elapsed * animationSpeed.current * 0.7
        mesh.rotation.z = elapsed * animationSpeed.current * 0.3
        break
        
      default:
        // No animation
        break
    }
  })

  // Create geometry
  const createGeometry = () => {
    const { type, args } = objectData.geometry
    
    switch (type) {
      case 'box': return <boxGeometry args={args} />
      case 'sphere': return <sphereGeometry args={args} />
      case 'cylinder': return <cylinderGeometry args={args} />
      case 'cone': return <coneGeometry args={args} />
      case 'torus': return <torusGeometry args={args} />
      case 'octahedron': return <octahedronGeometry args={args} />
      case 'dodecahedron': return <dodecahedronGeometry args={args} />
      case 'icosahedron': return <icosahedronGeometry args={args} />
      default: return <boxGeometry args={[0.1, 0.1, 0.1]} />
    }
  }

  // Create material
  const createMaterial = () => {
    const { type, color, transparent, opacity, metalness, roughness } = objectData.material
    
    const materialProps = {
      color: color,
      transparent,
      opacity
    }

    switch (type) {
      case 'basic': 
        return <meshBasicMaterial {...materialProps} />
      case 'standard': 
        return <meshStandardMaterial {...materialProps} metalness={metalness} roughness={roughness} />
      case 'phong': 
        return <meshPhongMaterial {...materialProps} />
      case 'lambert': 
        return <meshLambertMaterial {...materialProps} />
      default: 
        return <meshBasicMaterial {...materialProps} />
    }
  }

  return (
    <mesh
      ref={meshRef}
      position={position}
      scale={objectData.scale}
      rotation={[objectData.rotation.x, objectData.rotation.y, objectData.rotation.z]}
      onClick={() => onRemove?.(objectData.id)}
    >
      {createGeometry()}
      {createMaterial()}
    </mesh>
  )
}

// AR object manager component
export default function ARObjectManager({ 
  maxObjects = 20,
  autoRemove = true,
  autoRemoveDelay = 15000,
  onObjectAdded,
  onObjectRemoved 
}) {
  const [objects, setObjects] = useState([])

  // Add object at position
  const addObject = useCallback((position, hitPose = null) => {
    const objectData = RandomObjectGenerator.generateRandomObject()
    
    const newObject = {
      ...objectData,
      position: Array.isArray(position) ? position : [position.x, position.y, position.z],
      hitPose,
      createdAt: Date.now()
    }

    setObjects(prev => {
      const updated = [...prev, newObject]
      
      // Remove oldest objects if exceeding max
      if (updated.length > maxObjects) {
        const removed = updated.splice(0, updated.length - maxObjects)
        removed.forEach(obj => onObjectRemoved?.(obj))
      }
      
      return updated
    })

    console.log(`🎲 Added random ${objectData.geometry.type} at position:`, newObject.position)
    onObjectAdded?.(newObject)
    
    return newObject
  }, [maxObjects, onObjectAdded, onObjectRemoved])

  // Remove object by ID
  const removeObject = useCallback((objectId) => {
    setObjects(prev => {
      const objectToRemove = prev.find(obj => obj.id === objectId)
      if (objectToRemove) {
        console.log(`🗑️ Removed object:`, objectToRemove.id)
        onObjectRemoved?.(objectToRemove)
      }
      return prev.filter(obj => obj.id !== objectId)
    })
  }, [onObjectRemoved])

  // Clear all objects
  const clearAllObjects = useCallback(() => {
    console.log('🧹 Clearing all objects')
    objects.forEach(obj => onObjectRemoved?.(obj))
    setObjects([])
  }, [objects, onObjectRemoved])

  // Get object count
  const getObjectCount = () => objects.length

  // Expose methods
  const objectManager = {
    addObject,
    removeObject,
    clearAllObjects,
    getObjectCount,
    objects
  }

  return (
    <group name="ARObjectManager">
      {objects.map(obj => (
        <ARObject
          key={obj.id}
          objectData={obj}
          position={obj.position}
          onRemove={removeObject}
          autoRemove={autoRemove}
          autoRemoveDelay={autoRemoveDelay}
        />
      ))}
      
      {/* Expose manager to parent via ref */}
      <primitive object={{ objectManager }} ref={(ref) => {
        if (ref && ref.objectManager) {
          // Make manager available globally if needed
          window.arObjectManager = ref.objectManager
        }
      }} />
    </group>
  )
}

// Hook for accessing object manager
export function useARObjectManager() {
  return window.arObjectManager || null
}
